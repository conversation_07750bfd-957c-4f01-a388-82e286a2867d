[{"id": "wf-001-uuid", "routing_id": 1, "workflow_type": "sample_routing", "current_stage": "completed", "created_at": "2024-01-15T09:00:00", "created_by": 15, "updated_at": "2024-01-16T15:30:00", "stage_history": [{"stage_id": "initiated", "entered_at": "2024-01-15T09:00:00", "entered_by": 15, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2024-01-15T09:00:00", "entered_by": 15, "notes": "Submitted for approval", "metadata": {}}, {"stage_id": "approved", "entered_at": "2024-01-15T10:30:00", "entered_by": 1, "notes": "Approved for urgent processing", "metadata": {}}, {"stage_id": "in_transit", "entered_at": "2024-01-15T14:00:00", "entered_by": 15, "notes": "Dispatched via express courier", "metadata": {"courier_name": "Express Medical Courier", "courier_contact": "**********"}}, {"stage_id": "delivered", "entered_at": "2024-01-16T09:45:00", "entered_by": 1, "notes": "<PERSON>ple received in good condition", "metadata": {"condition": "good"}}, {"stage_id": "completed", "entered_at": "2024-01-16T15:30:00", "entered_by": 1, "notes": "Testing completed successfully", "metadata": {}}]}, {"id": "wf-002-uuid", "routing_id": 2, "workflow_type": "sample_routing", "current_stage": "in_transit", "created_at": "2024-01-17T11:00:00", "created_by": 17, "updated_at": "2024-01-17T16:00:00", "stage_history": [{"stage_id": "initiated", "entered_at": "2024-01-17T11:00:00", "entered_by": 17, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2024-01-17T11:00:00", "entered_by": 17, "notes": "Submitted for approval", "metadata": {}}, {"stage_id": "approved", "entered_at": "2024-01-17T12:30:00", "entered_by": 1, "notes": "Approved for molecular testing", "metadata": {}}, {"stage_id": "in_transit", "entered_at": "2024-01-17T16:00:00", "entered_by": 17, "notes": "Dispatched with dry ice", "metadata": {"courier_name": "BioLogistics", "courier_contact": "**********"}}]}, {"id": "wf-003-uuid", "routing_id": 3, "workflow_type": "sample_routing", "current_stage": "pending_approval", "created_at": "2024-01-18T08:30:00", "created_by": 19, "updated_at": "2024-01-18T08:30:00", "stage_history": [{"stage_id": "initiated", "entered_at": "2024-01-18T08:30:00", "entered_by": 19, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2024-01-18T08:30:00", "entered_by": 19, "notes": "Submitted for approval", "metadata": {}}]}, {"id": "wf-004-uuid", "routing_id": 4, "workflow_type": "sample_routing", "current_stage": "approved", "created_at": "2024-01-18T14:00:00", "created_by": 21, "updated_at": "2024-01-18T15:45:00", "stage_history": [{"stage_id": "initiated", "entered_at": "2024-01-18T14:00:00", "entered_by": 21, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2024-01-18T14:00:00", "entered_by": 21, "notes": "Submitted for approval", "metadata": {}}, {"stage_id": "approved", "entered_at": "2024-01-18T15:45:00", "entered_by": 25, "notes": "Approved for histopathology review", "metadata": {}}]}, {"id": "wf-005-uuid", "routing_id": 5, "workflow_type": "sample_routing", "current_stage": "rejected", "created_at": "2024-01-19T10:00:00", "created_by": 23, "updated_at": "2024-01-19T11:30:00", "stage_history": [{"stage_id": "initiated", "entered_at": "2024-01-19T10:00:00", "entered_by": 23, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2024-01-19T10:00:00", "entered_by": 23, "notes": "Submitted for approval", "metadata": {}}, {"stage_id": "rejected", "entered_at": "2024-01-19T11:30:00", "entered_by": 1, "notes": "Sample contaminated during collection", "metadata": {}}]}, {"id": "a8df6578-3ca5-4433-96bf-f9f1381b708e", "routing_id": 6, "workflow_type": "sample_routing", "current_stage": "approved", "created_at": "2025-06-14T22:11:23.552285", "created_by": 19, "updated_at": "2025-06-15T08:57:32.023035", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-14T22:11:23.552285", "entered_by": 19, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2025-06-14T22:11:23.552285", "entered_by": 19, "notes": "Submitted for approval", "metadata": {}}, {"stage_id": "approved", "entered_at": "2025-06-15T08:57:32.023035", "entered_by": 19, "notes": "", "metadata": {}}]}, {"id": "592a92e3-e6d5-473d-842c-384a71ceb889", "routing_id": 7, "workflow_type": "sample_routing", "current_stage": "approved", "created_at": "2025-06-14T22:11:58.753759", "created_by": 19, "updated_at": "2025-06-15T09:11:47.944276", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-14T22:11:58.753759", "entered_by": 19, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2025-06-14T22:11:58.753759", "entered_by": 19, "notes": "Submitted for approval", "metadata": {}}, {"stage_id": "approved", "entered_at": "2025-06-15T09:11:47.944276", "entered_by": 2, "notes": "Approved via test", "metadata": {}}]}, {"id": "985180b4-6291-4494-9c7f-aef2473c98bc", "routing_id": 8, "workflow_type": "sample_routing", "current_stage": "pending_approval", "created_at": "2025-06-14T22:16:36.575094", "created_by": 19, "updated_at": "2025-06-14T22:16:36.575094", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-14T22:16:36.575094", "entered_by": 19, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2025-06-14T22:16:36.575094", "entered_by": 19, "notes": "Submitted for approval", "metadata": {}}]}, {"id": "0da3ceef-33a1-4b47-8f08-65e81d32498a", "routing_id": 9, "workflow_type": "sample_routing", "current_stage": "pending_approval", "created_at": "2025-06-14T22:18:02.140910", "created_by": 19, "updated_at": "2025-06-14T22:18:02.140910", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-14T22:18:02.140910", "entered_by": 19, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2025-06-14T22:18:02.140910", "entered_by": 19, "notes": "Submitted for approval", "metadata": {}}]}, {"id": "0a0b74a8-1c4e-4c59-b9fe-b3963a1633c7", "routing_id": 10, "workflow_type": "sample_routing", "current_stage": "pending_approval", "created_at": "2025-06-14T22:22:03.649419", "created_by": 19, "updated_at": "2025-06-14T22:22:03.649419", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-14T22:22:03.649419", "entered_by": 19, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2025-06-14T22:22:03.649419", "entered_by": 19, "notes": "Submitted for approval", "metadata": {}}]}, {"id": "93bc9960-f0c3-4b94-91f0-b562158e3ae5", "routing_id": 11, "workflow_type": "sample_routing", "current_stage": "pending_approval", "created_at": "2025-06-14T22:25:19.943299", "created_by": 19, "updated_at": "2025-06-14T22:25:19.941928", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-14T22:25:19.941928", "entered_by": 19, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2025-06-14T22:25:19.941928", "entered_by": 19, "notes": "Submitted for approval", "metadata": {}}]}, {"id": "72ae3040-dd4a-4a53-9b46-954e8967d244", "routing_id": 12, "workflow_type": "sample_routing", "current_stage": "pending_approval", "created_at": "2025-06-15T08:52:24.829491", "created_by": 2, "updated_at": "2025-06-15T08:52:24.828325", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-15T08:52:24.828325", "entered_by": 2, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2025-06-15T08:52:24.828325", "entered_by": 2, "notes": "Submitted for approval", "metadata": {}}]}, {"id": "b4c2488c-5e87-4e8a-ad85-56e61835315b", "routing_id": 13, "workflow_type": "sample_routing", "current_stage": "completed", "created_at": "2025-06-15T09:01:04.063809", "created_by": 19, "updated_at": "2025-06-15T09:19:50.747246", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-15T09:01:04.063809", "entered_by": 19, "notes": "Routing initiated", "metadata": {}}, {"stage_id": "pending_approval", "entered_at": "2025-06-15T09:01:04.063809", "entered_by": 19, "notes": "Submitted for approval", "metadata": {}}, {"stage_id": "approved", "entered_at": "2025-06-15T09:01:26.414703", "entered_by": 2, "notes": "df", "metadata": {}}, {"stage_id": "in_transit", "entered_at": "2025-06-15T09:19:17.783201", "entered_by": 19, "notes": "sds", "metadata": {"courier_name": "sds", "courier_contact": "sds"}}, {"stage_id": "delivered", "entered_at": "2025-06-15T09:19:43.532571", "entered_by": 2, "notes": "", "metadata": {"condition": "good"}}, {"stage_id": "completed", "entered_at": "2025-06-15T09:19:50.747246", "entered_by": 2, "notes": "sd", "metadata": {}}]}, {"id": "e586f88d-c42d-4180-b748-96845a904ccb", "routing_id": 14, "workflow_type": "sample_routing", "current_stage": "completed", "created_at": "2025-06-15T09:24:50.339640", "created_by": 19, "updated_at": "2025-06-15T09:28:33.933404", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-15T09:24:50.339640", "entered_by": 19, "notes": "Workflow initiated"}, {"stage_id": "pending_approval", "entered_at": "2025-06-15T09:24:50.339640", "entered_by": 19, "notes": "Routing created and submitted for approval", "metadata": {}}, {"stage_id": "approved", "entered_at": "2025-06-15T09:27:24.253746", "entered_by": 2, "notes": "df", "metadata": {"approved_by": 2}}, {"stage_id": "in_transit", "entered_at": "2025-06-15T09:27:58.384531", "entered_by": 19, "notes": "fgfg", "metadata": {"dispatched_by": 19, "courier_name": "gfg", "courier_contact": "fgf"}}, {"stage_id": "delivered", "entered_at": "2025-06-15T09:28:26.850921", "entered_by": 2, "notes": "<PERSON><PERSON> received", "metadata": {"received_by": 2, "condition": "good"}}, {"stage_id": "completed", "entered_at": "2025-06-15T09:28:33.933404", "entered_by": 2, "notes": "df", "metadata": {"completed_by": 2}}]}, {"id": "4663fd90-c771-454e-9340-dd3e29800891", "routing_id": 15, "workflow_type": "sample_routing", "current_stage": "pending_approval", "created_at": "2025-06-15T09:26:23.452756", "created_by": 19, "updated_at": "2025-06-15T09:26:23.452756", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-15T09:26:23.452756", "entered_by": 19, "notes": "Workflow initiated"}, {"stage_id": "pending_approval", "entered_at": "2025-06-15T09:26:23.452756", "entered_by": 19, "notes": "Routing created and submitted for approval", "metadata": {}}]}, {"id": "b3326ae4-ea05-473b-a27a-93382a4fe663", "routing_id": 16, "workflow_type": "sample_routing", "current_stage": "pending_approval", "created_at": "2025-06-15T09:54:54.039601", "created_by": 19, "updated_at": "2025-06-15T09:54:54.039601", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-15T09:54:54.039601", "entered_by": 19, "notes": "Workflow initiated"}, {"stage_id": "pending_approval", "entered_at": "2025-06-15T09:54:54.039601", "entered_by": 19, "notes": "Routing created and submitted for approval", "metadata": {}}]}, {"id": "ce2a241e-4ae2-4847-a270-49129a3fa044", "routing_id": 17, "workflow_type": "sample_routing", "current_stage": "completed", "created_at": "2025-06-15T10:16:10.667631", "created_by": 19, "updated_at": "2025-06-15T11:36:19.422066", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-15T10:16:10.667631", "entered_by": 19, "notes": "Workflow initiated"}, {"stage_id": "pending_approval", "entered_at": "2025-06-15T10:16:10.683155", "entered_by": 19, "notes": "Routing created and submitted for approval", "metadata": {}}, {"stage_id": "approved", "entered_at": "2025-06-15T11:32:14.946805", "entered_by": 2, "notes": "gf", "metadata": {"approved_by": 2}}, {"stage_id": "in_transit", "entered_at": "2025-06-15T11:32:57.212756", "entered_by": 19, "notes": "df", "metadata": {"dispatched_by": 19, "courier_name": "dc", "courier_contact": "df"}}, {"stage_id": "delivered", "entered_at": "2025-06-15T11:36:12.906697", "entered_by": 2, "notes": "<PERSON><PERSON> received", "metadata": {"received_by": 2, "condition": "good"}}, {"stage_id": "completed", "entered_at": "2025-06-15T11:36:19.422066", "entered_by": 2, "notes": "Routing completed", "metadata": {"completed_by": 2}}]}, {"id": "431272f9-7e73-4718-b9dc-9f3304fc2dae", "routing_id": 18, "workflow_type": "sample_routing", "current_stage": "approved", "created_at": "2025-06-25T17:19:27.685018", "created_by": 4, "updated_at": "2025-06-25T17:21:28.355158", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-06-25T17:19:27.685018", "entered_by": 4, "notes": "Workflow initiated"}, {"stage_id": "pending_approval", "entered_at": "2025-06-25T17:19:27.687393", "entered_by": 4, "notes": "Routing created and submitted for approval", "metadata": {}}, {"stage_id": "approved", "entered_at": "2025-06-25T17:21:28.355158", "entered_by": 5, "notes": "Routing approved", "metadata": {"approved_by": 5}}]}, {"id": "d2f17633-cfa7-4a9a-a98c-a18289563c11", "routing_id": 19, "workflow_type": "sample_routing", "current_stage": "completed", "created_at": "2025-07-21T14:22:30.884032", "created_by": 4, "updated_at": "2025-07-21T14:41:25.051375", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-07-21T14:22:30.884051", "entered_by": 4, "notes": "Workflow initiated"}, {"stage_id": "pending_approval", "entered_at": "2025-07-21T14:22:30.922585", "entered_by": 4, "notes": "Routing created and submitted for approval", "metadata": {}}, {"stage_id": "approved", "entered_at": "2025-07-21T14:26:39.224945", "entered_by": 5, "notes": "testing", "metadata": {"approved_by": 5}}, {"stage_id": "in_transit", "entered_at": "2025-07-21T14:37:30.048052", "entered_by": 4, "notes": "<PERSON><PERSON> dispatched", "metadata": {"dispatched_by": 4, "courier_name": "wej", "courier_contact": "***********"}}, {"stage_id": "delivered", "entered_at": "2025-07-21T14:39:24.418378", "entered_by": 5, "notes": "<PERSON>ple received in good condition", "metadata": {"received_by": 5, "condition": "good"}}, {"stage_id": "completed", "entered_at": "2025-07-21T14:41:25.051390", "entered_by": 5, "notes": "Routing completed", "metadata": {"completed_by": 5}}]}, {"id": "1bb459f9-48db-46e1-a486-6062535727d0", "routing_id": 20, "workflow_type": "sample_routing", "current_stage": "pending_approval", "created_at": "2025-07-21T17:30:29.271718", "created_by": 4, "updated_at": "2025-07-21T17:30:29.287293", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-07-21T17:30:29.271725", "entered_by": 4, "notes": "Workflow initiated"}, {"stage_id": "pending_approval", "entered_at": "2025-07-21T17:30:29.287301", "entered_by": 4, "notes": "Routing created and submitted for approval", "metadata": {}}]}, {"id": "47ef7ece-3539-4d7f-b30a-9a1479b7270b", "routing_id": 21, "workflow_type": "sample_routing", "current_stage": "pending_approval", "created_at": "2025-07-21T17:32:09.273185", "created_by": 4, "updated_at": "2025-07-21T17:32:09.301715", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-07-21T17:32:09.273194", "entered_by": 4, "notes": "Workflow initiated"}, {"stage_id": "pending_approval", "entered_at": "2025-07-21T17:32:09.301726", "entered_by": 4, "notes": "Routing created and submitted for approval", "metadata": {}}]}, {"id": "18e38c1f-f5e0-446b-bea1-6ca15f23dd23", "routing_id": 22, "workflow_type": "sample_routing", "current_stage": "approved", "created_at": "2025-07-21T18:15:52.542563", "created_by": 4, "updated_at": "2025-07-21T18:22:37.351190", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-07-21T18:15:52.542591", "entered_by": 4, "notes": "Workflow initiated"}, {"stage_id": "pending_approval", "entered_at": "2025-07-21T18:15:52.558559", "entered_by": 4, "notes": "Routing created and submitted for approval", "metadata": {}}, {"stage_id": "approved", "entered_at": "2025-07-21T18:22:37.351200", "entered_by": 5, "notes": "check", "metadata": {"approved_by": 5}}]}, {"id": "71e44f69-8d1c-4beb-813a-45b2b18a1f09", "routing_id": 23, "workflow_type": "sample_routing", "current_stage": "pending_approval", "created_at": "2025-08-04T18:29:05.378182", "created_by": 4, "updated_at": "2025-08-04T18:29:05.426388", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-08-04T18:29:05.378205", "entered_by": 4, "notes": "Workflow initiated"}, {"stage_id": "pending_approval", "entered_at": "2025-08-04T18:29:05.426403", "entered_by": 4, "notes": "Routing created and submitted for approval", "metadata": {}}]}, {"id": "6e1617b4-a4a2-4d4b-bac6-e32ebc349006", "routing_id": 24, "workflow_type": "sample_routing", "current_stage": "completed", "created_at": "2025-08-04T18:29:59.087155", "created_by": 4, "updated_at": "2025-08-04T18:33:25.872027", "stage_history": [{"stage_id": "initiated", "entered_at": "2025-08-04T18:29:59.087177", "entered_by": 4, "notes": "Workflow initiated"}, {"stage_id": "pending_approval", "entered_at": "2025-08-04T18:29:59.137652", "entered_by": 4, "notes": "Routing created and submitted for approval", "metadata": {}}, {"stage_id": "approved", "entered_at": "2025-08-04T18:31:02.424457", "entered_by": 5, "notes": "yes", "metadata": {"approved_by": 5}}, {"stage_id": "in_transit", "entered_at": "2025-08-04T18:32:27.846025", "entered_by": 4, "notes": "check", "metadata": {"dispatched_by": 4, "courier_name": "test", "courier_contact": "test"}}, {"stage_id": "delivered", "entered_at": "2025-08-04T18:33:14.658538", "entered_by": 5, "notes": "test", "metadata": {"received_by": 5, "condition": "good"}}, {"stage_id": "completed", "entered_at": "2025-08-04T18:33:25.872047", "entered_by": 5, "notes": "chekc", "metadata": {"completed_by": 5}}]}]