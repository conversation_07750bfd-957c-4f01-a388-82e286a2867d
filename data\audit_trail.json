[{"id": "AUD_20250620_100602_700", "timestamp": "2025-06-20T10:06:02.234073", "event_type": "report_generation_started", "user_id": 1, "tenant_id": 1, "success": true, "details": {"billing_id": 40}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}, {"id": "AUD_20250620_100602_700", "timestamp": "2025-06-20T10:06:02.234073", "event_type": "report_generation_failed", "user_id": 1, "tenant_id": 1, "success": false, "details": {"billing_id": 40, "error": "billing_not_found"}, "ip_address": "127.0.0.1", "user_agent": "AVINI Labs System"}]